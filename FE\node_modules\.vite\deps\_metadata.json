{"hash": "9adce70b", "configHash": "5b1a9cfb", "lockfileHash": "8a3ed1e9", "browserHash": "845413d9", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "85f7696f", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "a6436c76", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "d5a2f13d", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "995970c1", "needsInterop": true}, "@heroicons/react/24/outline": {"src": "../../@heroicons/react/24/outline/esm/index.js", "file": "@heroicons_react_24_outline.js", "fileHash": "4c7c6bd8", "needsInterop": false}, "@material-tailwind/react": {"src": "../../@material-tailwind/react/index.js", "file": "@material-tailwind_react.js", "fileHash": "5b791f75", "needsInterop": true}, "@microsoft/signalr": {"src": "../../@microsoft/signalr/dist/esm/index.js", "file": "@microsoft_signalr.js", "fileHash": "abfdda11", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "62b2be14", "needsInterop": false}, "@react-oauth/google": {"src": "../../@react-oauth/google/dist/index.esm.js", "file": "@react-oauth_google.js", "fileHash": "9f067c80", "needsInterop": false}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "78067b1c", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "2b8f5e30", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "7c70a97e", "needsInterop": false}, "date-fns": {"src": "../../date-fns/index.js", "file": "date-fns.js", "fileHash": "3c1a19cb", "needsInterop": false}, "firebase/app": {"src": "../../firebase/app/dist/esm/index.esm.js", "file": "firebase_app.js", "fileHash": "4fdb4d57", "needsInterop": false}, "firebase/auth": {"src": "../../firebase/auth/dist/esm/index.esm.js", "file": "firebase_auth.js", "fileHash": "a7ce598f", "needsInterop": false}, "flatpickr": {"src": "../../flatpickr/dist/esm/index.js", "file": "flatpickr.js", "fileHash": "f8b5758f", "needsInterop": false}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "9884c0b0", "needsInterop": false}, "jwt-decode": {"src": "../../jwt-decode/build/esm/index.js", "file": "jwt-decode.js", "fileHash": "84297760", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "6f588906", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "98b2d7c6", "needsInterop": true}, "react-quill": {"src": "../../react-quill/lib/index.js", "file": "react-quill.js", "fileHash": "32744d4d", "needsInterop": true}, "react-router": {"src": "../../react-router/dist/development/index.mjs", "file": "react-router.js", "fileHash": "0029e3b2", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "3de4717b", "needsInterop": false}, "react-toastify": {"src": "../../react-toastify/dist/index.mjs", "file": "react-toastify.js", "fileHash": "ddd3cc80", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "eccc7250", "needsInterop": false}}, "chunks": {"chunk-6EBK7ZE7": {"file": "chunk-6EBK7ZE7.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-QATYC7JX": {"file": "chunk-QATYC7JX.js"}, "chunk-IULEFUF5": {"file": "chunk-IULEFUF5.js"}, "chunk-VF66GCQM": {"file": "chunk-VF66GCQM.js"}, "chunk-2GVRE4D3": {"file": "chunk-2GVRE4D3.js"}, "chunk-KPD4VVXB": {"file": "chunk-KPD4VVXB.js"}, "chunk-I773Y2XN": {"file": "chunk-I773Y2XN.js"}, "chunk-LK32TJAX": {"file": "chunk-LK32TJAX.js"}}}